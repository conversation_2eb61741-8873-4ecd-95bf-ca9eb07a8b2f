{"tests/test_company_api.py::TestCompanyAPI::test_create_company_profile": true, "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_not_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile": true, "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile_not_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_no_company": true, "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_with_company": true, "tests/test_company_api.py::TestCompanyAPI::test_update_tax_settings": true, "tests/test_company_api.py::TestCompanyAPI::test_get_zus_settings_with_company": true, "tests/test_company_service.py::TestNIPValidation::test_valid_nip": true, "tests/test_company_service.py::TestCompanyService::test_create_company_profile": true, "tests/test_company_service.py::TestCompanyService::test_create_duplicate_company_profile": true, "tests/test_company_service.py::TestCompanyService::test_get_company_profile_empty": true, "tests/test_company_service.py::TestCompanyService::test_get_company_profile_exists": true}