["tests/test_company_api.py::TestCompanyAPI::test_create_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile_invalid_nip", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_no_company", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_get_zus_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_update_tax_settings", "tests/test_company_service.py::TestCompanyService::test_create_company_profile", "tests/test_company_service.py::TestCompanyService::test_create_company_profile_invalid_nip", "tests/test_company_service.py::TestCompanyService::test_create_duplicate_company_profile", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_empty", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_exists", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_checksum", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_length", "tests/test_company_service.py::TestNIPValidation::test_nip_with_formatting", "tests/test_company_service.py::TestNIPValidation::test_valid_nip"]