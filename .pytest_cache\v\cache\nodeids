["tests/test_client_api.py::TestClientAPI::test_create_client", "tests/test_client_api.py::TestClientAPI::test_create_client_invalid_nip", "tests/test_client_api.py::TestClientAPI::test_create_client_without_nip", "tests/test_client_api.py::TestClientAPI::test_delete_client", "tests/test_client_api.py::TestClientAPI::test_delete_client_not_found", "tests/test_client_api.py::TestClientAPI::test_get_client_by_id", "tests/test_client_api.py::TestClientAPI::test_get_client_by_id_not_found", "tests/test_client_api.py::TestClientAPI::test_get_clients_empty", "tests/test_client_api.py::TestClientAPI::test_get_clients_summary", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_data", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_pagination", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_search", "tests/test_client_api.py::TestClientAPI::test_search_client_by_nip", "tests/test_client_api.py::TestClientAPI::test_update_client", "tests/test_client_api.py::TestClientAPI::test_update_client_not_found", "tests/test_client_service.py::TestClientService::test_create_client", "tests/test_client_service.py::TestClientService::test_create_client_invalid_nip", "tests/test_client_service.py::TestClientService::test_create_client_without_nip", "tests/test_client_service.py::TestClientService::test_delete_client", "tests/test_client_service.py::TestClientService::test_delete_client_not_found", "tests/test_client_service.py::TestClientService::test_get_client_by_id", "tests/test_client_service.py::TestClientService::test_get_client_by_id_not_found", "tests/test_client_service.py::TestClientService::test_get_clients_pagination", "tests/test_client_service.py::TestClientService::test_get_clients_search", "tests/test_client_service.py::TestClientService::test_search_clients_by_nip", "tests/test_client_service.py::TestClientService::test_update_client", "tests/test_client_service.py::TestClientService::test_update_client_not_found", "tests/test_client_service.py::TestPaginationUtils::test_calculate_pagination_info", "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile_invalid_nip", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_no_company", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_get_zus_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_update_tax_settings", "tests/test_company_service.py::TestCompanyService::test_create_company_profile", "tests/test_company_service.py::TestCompanyService::test_create_company_profile_invalid_nip", "tests/test_company_service.py::TestCompanyService::test_create_duplicate_company_profile", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_empty", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_exists", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_checksum", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_length", "tests/test_company_service.py::TestNIPValidation::test_nip_with_formatting", "tests/test_company_service.py::TestNIPValidation::test_valid_nip"]