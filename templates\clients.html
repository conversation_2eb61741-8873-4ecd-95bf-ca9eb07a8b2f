{% extends "base.html" %}

{% block title %}Client Management - InFirma{% endblock %}

{% block content %}
<div class="card">
    <h2>👥 Client Management</h2>
    
    {% if request.query_params.get('success') == 'created' %}
    <div class="alert alert-success">
        <strong>Success!</strong> Client created successfully.
    </div>
    {% endif %}
    
    {% if request.query_params.get('error') %}
    <div class="alert alert-error">
        <strong>Error:</strong> {{ request.query_params.get('error') }}
    </div>
    {% endif %}
    
    <h3>Add New Client</h3>
    <form method="post" action="/web/clients/create">
        <div class="grid">
            <div class="form-group">
                <label for="name">Client Name *</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="nip">NIP Number (optional)</label>
                <input type="text" id="nip" name="nip" placeholder="1234563224">
            </div>
        </div>
        
        <div class="grid">
            <div class="form-group">
                <label for="street">Street Address *</label>
                <input type="text" id="street" name="street" required>
            </div>
            
            <div class="form-group">
                <label for="city">City *</label>
                <input type="text" id="city" name="city" required>
            </div>
            
            <div class="form-group">
                <label for="postal_code">Postal Code *</label>
                <input type="text" id="postal_code" name="postal_code" required placeholder="00-001">
            </div>
        </div>
        
        <div class="grid">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email">
            </div>
            
            <div class="form-group">
                <label for="phone">Phone</label>
                <input type="text" id="phone" name="phone">
            </div>
        </div>
        
        <button type="submit" class="btn btn-success">Add Client</button>
    </form>
</div>

<div class="card">
    <h3>Client List</h3>
    
    <!-- Search Form -->
    <form method="get" action="/web/clients" style="margin-bottom: 1rem;">
        <div style="display: flex; gap: 1rem; align-items: end;">
            <div class="form-group" style="flex: 1; margin-bottom: 0;">
                <label for="search">Search Clients</label>
                <input type="text" id="search" name="search" value="{{ search }}" placeholder="Search by name, city, or email...">
            </div>
            <button type="submit" class="btn">Search</button>
            {% if search %}
            <a href="/web/clients" class="btn" style="background: #95a5a6;">Clear</a>
            {% endif %}
        </div>
    </form>
    
    {% if clients %}
    <div class="result-table">
        <table class="result-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>NIP</th>
                    <th>City</th>
                    <th>Email</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for client in clients %}
                <tr>
                    <td><strong>{{ client.name }}</strong></td>
                    <td>{{ client.nip or "-" }}</td>
                    <td>{{ client.city }}</td>
                    <td>{{ client.email or "-" }}</td>
                    <td>
                        {% if client.is_active %}
                        <span style="color: #27ae60;">✅ Active</span>
                        {% else %}
                        <span style="color: #e74c3c;">❌ Inactive</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
    <div style="margin-top: 1rem; text-align: center;">
        <div style="display: inline-flex; gap: 0.5rem; align-items: center;">
            {% if pagination.has_prev %}
            <a href="/web/clients?page={{ pagination.page - 1 }}{% if search %}&search={{ search }}{% endif %}" class="btn">← Previous</a>
            {% endif %}
            
            <span style="padding: 0.5rem 1rem;">
                Page {{ pagination.page }} of {{ pagination.total_pages }} 
                ({{ pagination.total }} total clients)
            </span>
            
            {% if pagination.has_next %}
            <a href="/web/clients?page={{ pagination.page + 1 }}{% if search %}&search={{ search }}{% endif %}" class="btn">Next →</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% else %}
    <div class="alert alert-error">
        {% if search %}
        <strong>No clients found</strong> matching your search criteria "{{ search }}".
        {% else %}
        <strong>No clients found</strong><br>
        Add your first client using the form above.
        {% endif %}
    </div>
    {% endif %}
</div>

<div class="card">
    <h3>📊 Client Statistics</h3>
    <div class="grid">
        <div style="text-align: center;">
            <h4 style="color: #3498db; font-size: 2rem; margin-bottom: 0.5rem;">{{ pagination.total }}</h4>
            <p>Total Clients</p>
        </div>
        <div style="text-align: center;">
            <h4 style="color: #27ae60; font-size: 2rem; margin-bottom: 0.5rem;">{{ clients|selectattr("is_active")|list|length }}</h4>
            <p>Active Clients</p>
        </div>
        <div style="text-align: center;">
            <h4 style="color: #e74c3c; font-size: 2rem; margin-bottom: 0.5rem;">{{ clients|rejectattr("is_active")|list|length }}</h4>
            <p>Inactive Clients</p>
        </div>
    </div>
</div>
{% endblock %}
